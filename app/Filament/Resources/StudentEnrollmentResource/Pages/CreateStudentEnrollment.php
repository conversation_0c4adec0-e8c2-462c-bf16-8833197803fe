<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentEnrollmentResource\Pages;

use App\Enums\EnrollStat; // Import the EnrollStat enum
use App\Filament\Resources\ClassResource; // Import the new service
use App\Filament\Resources\StudentEnrollmentResource;
use App\Models\Classes;
use App\Models\Student;
use App\Models\StudentTuition;
use App\Services\EnrollmentService;
use App\Services\GeneralSettingsService; // Import Student model
use Exception;
use Filament\Notifications\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

final class CreateStudentEnrollment extends CreateRecord
{
    protected static string $resource = StudentEnrollmentResource::class;

    public function clearStudent($studentId): void
    {
        try {
            $student = Student::find($studentId);
            if (! $student) {
                Notification::make()
                    ->title('Error')
                    ->body('Student not found.')
                    ->danger()
                    ->send();

                return;
            }

            $user = Auth::user();
            $clearedBy = $user ? $user->name : 'System';

            // Use the method from the Student model to clear the student
            $result = $student->markClearanceAsCleared($clearedBy, 'Cleared via enrollment form');

            if ($result) {
                Notification::make()
                    ->title('Student Cleared')
                    ->body("Student {$student->full_name} has been successfully cleared.")
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Error')
                    ->body('Failed to clear the student. Please try again.')
                    ->danger()
                    ->send();
            }
        } catch (Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('An error occurred while clearing the student: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure the course_id from the selected student is included in the enrollment data
        if (isset($data['student_id'])) {
            $student = Student::find($data['student_id']);
            if ($student) {
                $data['course_id'] = $student->course_id; // Add course_id to the data being saved
            } else {
                // Handle case where student might not be found (should ideally not happen here)
                // You might throw an exception or set a default/null if the column is nullable
                Notification::make()
                    ->danger()
                    ->title('Student Not Found')
                    ->body('Could not find the selected student to retrieve course information.')
                    ->send();
                // Halt execution or handle error appropriately
                $this->halt();
            }
        }
        // Ensure school_year is set from GeneralSettingsService
        $settingsService = app(GeneralSettingsService::class);
        $data['school_year'] = $settingsService->getCurrentSchoolYearString(); // Add school_year

        // Ensure status is set if not already present
        if (! isset($data['status'])) {
            $data['status'] = EnrollStat::Pending->value; // Set default status
        }

        return $data;
    }

    private function beforeCreate(): void
    {
        $settingsService = app(GeneralSettingsService::class);
        $schoolYear = $settingsService->getCurrentSchoolYearString();
        $semester = $settingsService->getCurrentSemester();
        $academicYear = $this->data['academic_year'];
        $selectedCourse = $this->data['course_id'];

        // Fetch all relevant classes in a single query
        $classes = Classes::where('academic_year', $academicYear)
            ->where('semester', $semester)
            ->where('school_year', $schoolYear)
            ->whereJsonContains('course_codes', $selectedCourse)
            ->with('ClassStudents') // Eager load the relationships
            ->get()
            ->keyBy('subject_code');

        // Use the new EnrollmentService
        $enrollmentService = app(EnrollmentService::class); // Resolve the service from the container
        $fullClasses = $enrollmentService->checkFullClasses(
            $classes,
            collect($this->data['subjectsEnrolled'] ?? []) // Ensure it's an array/collection
        );

        if ($fullClasses->isNotEmpty()) {
            $this->showWarningNotification($fullClasses);
            $this->halt();
        }
    }

    private function afterCreate(): void
    {
        $record = $this->record;
        $data = $this->data;

        // Use the new EnrollmentService to create the StudentTuition record
        $enrollmentService = app(EnrollmentService::class); // Resolve the service
        $tuitionRecord = $enrollmentService->createStudentTuition($record, $data);

        if (! $tuitionRecord) {
            // Handle potential error during tuition creation (e.g., show notification)
            Notification::make()
                ->danger()
                ->title('Tuition Creation Failed')
                ->body('There was an error calculating or saving the student tuition details. Please check the logs.')
                ->send();
            // Optionally, you might want to halt or take other actions
        }
        // Tuition creation logic is now in EnrollmentServiceProvider->createStudentTuition
    }

    // Removed private checkFullClasses method - logic moved to EnrollmentServiceProvider

    private function showWarningNotification(Collection $fullClasses): void
    {
        $actions = $fullClasses
            ->map(function ($subjectCode): Action {
                $class = Classes::where('subject_code', $subjectCode)->first();

                return Action::make("edit_{$subjectCode}")
                    ->label("Edit {$subjectCode}")
                    ->url(
                        ClassResource::getUrl('edit', [ // Corrected class name
                            'record' => $class->id,
                        ])
                    )
                    ->button()
                    ->openUrlInNewTab();
            })
            ->all();

        Notification::make()
            ->warning()
            ->title('Warning: Some classes are full')
            ->body(
                'The following classes have no available slots: '.
                    $fullClasses->implode(', ')
            )
            ->actions($actions)
            ->sendToDatabase(Auth::user())
            ->persistent()
            ->send();
    }
}
